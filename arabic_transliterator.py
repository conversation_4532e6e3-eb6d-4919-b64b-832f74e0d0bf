#!/usr/bin/env python3
"""
Arabic Company Name Transliterator

This script transliterates Arabic company names to English using phonetic transliteration.
It preserves the original Arabic pronunciation while converting to Latin characters.

Author: AI Assistant
Date: 2025-07-23
"""

import re
import csv
import json
import sys
from pathlib import Path
from typing import List, Tuple, Dict

# Arabic to English transliteration mapping (improved)
ARABIC_TRANSLITERATION = {
    # Alif variations
    'ا': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'aa',
    # Basic consonants
    'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j',
    'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'dh',
    'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh',
    'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z',
    'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
    'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',
    'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a',
    # Special characters
    'ة': 'h', 'ء': '', 'ئ': 'i', 'ؤ': 'u',
    # Common combinations (process these first)
    'ال': 'Al', 'لا': 'La',
    # Diacritics (usually not present but just in case)
    'َ': 'a', 'ُ': 'u', 'ِ': 'i', 'ً': 'an', 'ٌ': 'un', 'ٍ': 'in',
    'ْ': '', 'ّ': '', 'ـ': ''
}

# Common Arabic business terms and their English equivalents
BUSINESS_TERMS = {
    'ش.م.م': 'LLC',
    'ش م م': 'LLC', 
    'ش.م.ع.ع': 'SAOG',
    'ش م ع ع': 'SAOG',
    'ش.م.ع.م': 'SAOC',
    'ش م ع م': 'SAOC',
    'ش ش و': 'Partnership',
    'توصية': 'Limited Partnership',
    'للتجارة': 'Trading',
    'والمقاولات': 'and Contracting',
    'والتجارة': 'and Trading',
    'للمشاريع': 'Projects',
    'الشركة': 'Company',
    'مؤسسة': 'Foundation',
    'مركز': 'Center',
    'معهد': 'Institute',
    'أكاديمية': 'Academy',
    'جامعة': 'University',
    'كلية': 'College',
    'وزارة': 'Ministry',
    'هيئة': 'Authority',
    'مجموعة': 'Group',
    'شركة': 'Company'
}

def is_arabic(text: str) -> bool:
    """Check if text contains Arabic characters."""
    arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
    return bool(arabic_pattern.search(text))

def transliterate_arabic_text(text: str) -> str:
    """
    Transliterate Arabic text to English using phonetic mapping.

    Args:
        text: Input text containing Arabic characters

    Returns:
        Transliterated text in English characters
    """
    if not text or not is_arabic(text):
        return text

    result = text

    # Handle common combinations first (longer patterns before shorter ones)
    combinations = ['ال', 'لا']
    for combo in combinations:
        if combo in ARABIC_TRANSLITERATION:
            result = result.replace(combo, ARABIC_TRANSLITERATION[combo])

    # Then handle individual characters
    for arabic, english in ARABIC_TRANSLITERATION.items():
        if arabic not in combinations:  # Skip already processed combinations
            result = result.replace(arabic, english)

    # Clean up the result
    result = re.sub(r'([aeiou])\1+', r'\1', result)  # Remove duplicate vowels
    result = re.sub(r'([bcdfghjklmnpqrstvwxyz])\1+', r'\1', result)  # Remove duplicate consonants
    result = re.sub(r'\s+', ' ', result).strip()  # Clean up spaces

    return result

def process_business_terms(text: str) -> str:
    """Replace common Arabic business terms with English equivalents."""
    result = text
    for arabic_term, english_term in BUSINESS_TERMS.items():
        result = result.replace(arabic_term, english_term)
    return result

def transliterate_company_name(company_name: str) -> str:
    """
    Transliterate a complete company name, handling mixed Arabic/English content.
    
    Args:
        company_name: Original company name in Arabic
        
    Returns:
        Transliterated company name in English
    """
    if not company_name or not company_name.strip():
        return company_name
    
    # First, handle business terms
    processed = process_business_terms(company_name)
    
    # Split into words and process each word
    words = processed.split()
    transliterated_words = []
    
    for word in words:
        if is_arabic(word):
            transliterated = transliterate_arabic_text(word)
            # Capitalize first letter of each word
            if transliterated:
                transliterated = transliterated.capitalize()
            transliterated_words.append(transliterated)
        else:
            # Keep non-Arabic words as-is
            transliterated_words.append(word)
    
    result = ' '.join(transliterated_words)
    
    # Clean up extra spaces
    result = re.sub(r'\s+', ' ', result).strip()
    
    return result

def read_company_names(file_path: str) -> List[str]:
    """Read company names from input file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return [line.strip() for line in file.readlines()]
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading file: {e}")
        sys.exit(1)

def save_results(results: List[Tuple[str, str]], output_format: str = 'csv'):
    """Save transliteration results to file."""
    if output_format.lower() == 'csv':
        output_file = 'transliterated_companies.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Original Arabic', 'Transliterated English'])
            writer.writerows(results)
    
    elif output_format.lower() == 'tsv':
        output_file = 'transliterated_companies.tsv'
        with open(output_file, 'w', encoding='utf-8') as tsvfile:
            tsvfile.write('Original Arabic\tTransliterated English\n')
            for original, transliterated in results:
                tsvfile.write(f'{original}\t{transliterated}\n')
    
    print(f"Results saved to: {output_file}")
    return output_file

def main():
    """Main function to process Arabic company names."""
    input_file = 'records.txt'
    
    print("Arabic Company Name Transliterator")
    print("=" * 40)
    
    # Read company names
    print(f"Reading company names from: {input_file}")
    company_names = read_company_names(input_file)
    print(f"Found {len(company_names)} entries")
    
    # Process transliterations
    print("Processing transliterations...")
    results = []
    
    for i, company_name in enumerate(company_names, 1):
        if company_name:  # Skip empty lines
            transliterated = transliterate_company_name(company_name)
            results.append((company_name, transliterated))
            
            # Show progress for every 100 entries
            if i % 100 == 0:
                print(f"Processed {i} entries...")
    
    print(f"Completed processing {len(results)} company names")
    
    # Save results in both CSV and TSV formats
    print("\nSaving results...")
    save_results(results, 'csv')
    save_results(results, 'tsv')
    
    # Show sample results
    print("\nSample transliterations:")
    print("-" * 60)
    for i, (original, transliterated) in enumerate(results[:10]):
        print(f"{i+1:2d}. {original}")
        print(f"    → {transliterated}")
        print()
    
    if len(results) > 10:
        print(f"... and {len(results) - 10} more entries")
    
    print("\nTransliteration complete!")
    print("Files created:")
    print("- transliterated_companies.csv (for Excel import)")
    print("- transliterated_companies.tsv (for tab-separated pasting)")

if __name__ == "__main__":
    main()
