#!/usr/bin/env python3
"""
Enhanced Arabic Company Name Transliterator

This enhanced version provides better transliteration with improved vowel handling
and more readable output for Arabic company names.

Author: AI Assistant
Date: 2025-07-23
"""

import re
import csv
import sys
from typing import List, Tuple, Dict

# Enhanced Arabic to English transliteration mapping
ARABIC_TRANSLITERATION = {
    # Handle definite article first
    'ال': 'Al',
    # Alif variations
    'ا': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'aa',
    # Basic consonants with better vowel handling
    'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j',
    'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'dh',
    'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh',
    'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z',
    'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
    'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',
    'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a',
    'ة': 'ah', 'ء': '', 'ئ': 'i', 'ؤ': 'u',
    'لا': 'la'
}

# Enhanced business terms dictionary
BUSINESS_TERMS = {
    'ش.م.م': 'LLC',
    'ش م م': 'LLC', 
    'ش.م.ع.ع': 'SAOG',
    'ش م ع ع': 'SAOG',
    'ش.م.ع.م': 'SAOC',
    'ش م ع م': 'SAOC',
    'ش ش و': 'Partnership',
    'توصية': 'Limited Partnership',
    'للتجارة': 'for Trading',
    'والمقاولات': 'and Contracting',
    'والتجارة': 'and Trading',
    'للمشاريع': 'for Projects',
    'الشركة': 'Al Sharika',
    'شركة': 'Sharika',
    'مؤسسة': 'Muassasa',
    'مركز': 'Markaz',
    'معهد': 'Mahad',
    'أكاديمية': 'Academy',
    'جامعة': 'Jamia',
    'كلية': 'Kuliya',
    'وزارة': 'Wizara',
    'هيئة': 'Haia',
    'مجموعة': 'Majmua',
    'للخدمات': 'for Services',
    'والخدمات': 'and Services',
    'لتكنولوجيا': 'for Technology',
    'للتكنولوجيا': 'for Technology',
    'للتدريب': 'for Training',
    'والتطوير': 'and Development',
    'للاستثمار': 'for Investment',
    'والاستثمار': 'and Investment'
}

def is_arabic(text: str) -> bool:
    """Check if text contains Arabic characters."""
    arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
    return bool(arabic_pattern.search(text))

def add_vowels_intelligently(text: str) -> str:
    """Add vowels to make transliteration more readable."""
    # Add vowels between consonant clusters
    result = text
    
    # Add 'a' between certain consonant combinations
    consonant_patterns = [
        (r'([bcdfghjklmnpqrstvwxz])([bcdfghjklmnpqrstvwxz])', r'\1a\2'),
        (r'^([bcdfghjklmnpqrstvwxz])([bcdfghjklmnpqrstvwxz])', r'\1a\2'),
    ]
    
    for pattern, replacement in consonant_patterns:
        result = re.sub(pattern, replacement, result)
    
    return result

def transliterate_arabic_text(text: str) -> str:
    """Enhanced transliteration with better vowel handling."""
    if not text or not is_arabic(text):
        return text
    
    result = text
    
    # Handle definite article 'ال' first
    result = result.replace('ال', 'Al')
    
    # Handle other combinations
    for arabic, english in ARABIC_TRANSLITERATION.items():
        if arabic != 'ال':  # Already handled
            result = result.replace(arabic, english)
    
    # Add intelligent vowels
    result = add_vowels_intelligently(result)
    
    # Clean up
    result = re.sub(r'([aeiou])\1+', r'\1', result)  # Remove duplicate vowels
    result = re.sub(r'aa+', 'aa', result)  # Keep double 'a' but not more
    result = re.sub(r'\s+', ' ', result).strip()
    
    return result

def process_business_terms(text: str) -> str:
    """Replace Arabic business terms with English equivalents."""
    result = text
    # Sort by length (longest first) to avoid partial replacements
    sorted_terms = sorted(BUSINESS_TERMS.items(), key=lambda x: len(x[0]), reverse=True)
    
    for arabic_term, english_term in sorted_terms:
        result = result.replace(arabic_term, english_term)
    
    return result

def transliterate_company_name(company_name: str) -> str:
    """Enhanced company name transliteration."""
    if not company_name or not company_name.strip():
        return company_name
    
    # First handle business terms
    processed = process_business_terms(company_name)
    
    # Split into words and process each
    words = processed.split()
    transliterated_words = []
    
    for word in words:
        if is_arabic(word):
            transliterated = transliterate_arabic_text(word)
            if transliterated:
                # Capitalize first letter
                transliterated = transliterated.capitalize()
            transliterated_words.append(transliterated)
        else:
            # Keep non-Arabic words as-is
            transliterated_words.append(word)
    
    result = ' '.join(transliterated_words)
    result = re.sub(r'\s+', ' ', result).strip()
    
    return result

def read_company_names(file_path: str) -> List[str]:
    """Read company names from input file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return [line.strip() for line in file.readlines()]
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading file: {e}")
        sys.exit(1)

def save_results(results: List[Tuple[str, str]], output_format: str = 'csv'):
    """Save transliteration results to file."""
    if output_format.lower() == 'csv':
        output_file = 'enhanced_transliterated_companies.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Original Arabic', 'Enhanced Transliterated English'])
            writer.writerows(results)
    
    elif output_format.lower() == 'tsv':
        output_file = 'enhanced_transliterated_companies.tsv'
        with open(output_file, 'w', encoding='utf-8') as tsvfile:
            tsvfile.write('Original Arabic\tEnhanced Transliterated English\n')
            for original, transliterated in results:
                tsvfile.write(f'{original}\t{transliterated}\n')
    
    print(f"Enhanced results saved to: {output_file}")
    return output_file

def main():
    """Main function for enhanced transliteration."""
    input_file = 'records.txt'
    
    print("Enhanced Arabic Company Name Transliterator")
    print("=" * 45)
    
    # Read company names
    print(f"Reading company names from: {input_file}")
    company_names = read_company_names(input_file)
    print(f"Found {len(company_names)} entries")
    
    # Process transliterations
    print("Processing enhanced transliterations...")
    results = []
    
    for i, company_name in enumerate(company_names, 1):
        if company_name:  # Skip empty lines
            transliterated = transliterate_company_name(company_name)
            results.append((company_name, transliterated))
            
            if i % 100 == 0:
                print(f"Processed {i} entries...")
    
    print(f"Completed processing {len(results)} company names")
    
    # Save results
    print("\nSaving enhanced results...")
    save_results(results, 'csv')
    save_results(results, 'tsv')
    
    # Show sample results
    print("\nSample enhanced transliterations:")
    print("-" * 70)
    for i, (original, transliterated) in enumerate(results[:10]):
        print(f"{i+1:2d}. {original}")
        print(f"    → {transliterated}")
        print()
    
    if len(results) > 10:
        print(f"... and {len(results) - 10} more entries")
    
    print("\nEnhanced transliteration complete!")
    print("Files created:")
    print("- enhanced_transliterated_companies.csv")
    print("- enhanced_transliterated_companies.tsv")

if __name__ == "__main__":
    main()
