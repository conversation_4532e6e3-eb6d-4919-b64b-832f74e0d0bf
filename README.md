# Arabic Company Name Transliterator

A Python tool for transliterating Arabic company names to English using phonetic transliteration. This tool converts Arabic letters to their English alphabet equivalents while preserving the original pronunciation.

## Features

- **Phonetic Transliteration**: Converts Arabic text to English letters based on pronunciation
- **Business Term Recognition**: Automatically translates common Arabic business abbreviations
- **Mixed Content Handling**: Properly processes names with both Arabic and English text
- **Multiple Output Formats**: Generates both CSV and TSV files for easy Excel integration
- **Batch Processing**: Handles large lists of company names efficiently

## Quick Start

### Prerequisites
- Python 3.6 or higher
- No additional libraries required (uses Python standard library only)

### Installation
1. Download the script files to your project directory
2. Ensure your Arabic company names are in a file called `records.txt` (one name per line)

### Usage

1. **Prepare your data**: Make sure your Arabic company names are in `records.txt`, one name per line
2. **Run the transliterator**:
   ```bash
   python arabic_transliterator.py
   ```
3. **Get your results**: The script will create two output files:
   - `transliterated_companies.csv` - For importing into Excel
   - `transliterated_companies.tsv` - For copy-pasting into Excel

## Example Transliterations

| Original Arabic | Transliterated English |
|----------------|------------------------|
| الشمس | Alshams |
| مجوهرات عارف | Majwhrat Aarif |
| الشركة العمانية ش.م.م | Alshrkh Alamanih LLC |
| مؤسسة الباسم | Muasssh Albasim |
| معهد الخليج الحديث | Mahd Alkhalij Alhadith |

## Business Terms Dictionary

The tool automatically recognizes and translates common Arabic business terms:

| Arabic Term | English Translation |
|-------------|-------------------|
| ش.م.م | LLC |
| ش.م.ع.ع | SAOG |
| ش.م.ع.م | SAOC |
| توصية | Limited Partnership |
| للتجارة | Trading |
| والمقاولات | and Contracting |
| الشركة | Company |
| مؤسسة | Foundation |
| مركز | Center |
| معهد | Institute |

## Using Results in Excel

### Method 1: Import CSV File
1. Open Excel
2. Go to Data → Get Data → From File → From Text/CSV
3. Select `transliterated_companies.csv`
4. Follow the import wizard

### Method 2: Copy-Paste from TSV
1. Open `transliterated_companies.tsv` in a text editor
2. Copy the content
3. Paste into Excel (it will automatically separate into columns)

### Method 3: Add to Existing Excel File
1. Open your existing Excel file with Arabic company names
2. Create a new column for transliterated names
3. Copy the transliterated names from the output files
4. Paste into the new column

## File Structure

```
your-project/
├── arabic_transliterator.py    # Main transliteration script
├── requirements.txt            # Dependencies (none required)
├── README.md                  # This file
├── records.txt                # Your input file with Arabic names
├── transliterated_companies.csv  # Output: CSV format
└── transliterated_companies.tsv  # Output: TSV format
```

## Customization

### Adding Custom Business Terms
Edit the `BUSINESS_TERMS` dictionary in `arabic_transliterator.py` to add your own translations:

```python
BUSINESS_TERMS = {
    'your_arabic_term': 'Your English Translation',
    # ... existing terms
}
```

### Modifying Transliteration Rules
Edit the `ARABIC_TRANSLITERATION` dictionary to customize how specific Arabic letters are transliterated:

```python
ARABIC_TRANSLITERATION = {
    'ع': 'a',  # You can change this to 'aa' or other variants
    # ... other mappings
}
```

## Troubleshooting

### Common Issues

1. **File not found error**: Make sure `records.txt` exists in the same directory as the script
2. **Encoding issues**: Ensure your input file is saved with UTF-8 encoding
3. **Empty output**: Check that your input file contains Arabic text

### Getting Help

If you encounter issues:
1. Check that Python 3.6+ is installed: `python --version`
2. Verify your input file encoding is UTF-8
3. Make sure the input file has one company name per line

## Technical Details

### Transliteration Method
- Uses phonetic mapping of Arabic letters to English equivalents
- Handles Arabic diacritics and special characters
- Preserves word boundaries and capitalization
- Removes duplicate consecutive letters for cleaner output

### Character Encoding
- Input: UTF-8 encoded Arabic text
- Output: UTF-8 encoded files with English transliterations
- Compatible with Excel and other spreadsheet applications

## License

This tool is provided as-is for educational and business use. Feel free to modify and distribute according to your needs.

---

**Created by**: AI Assistant  
**Date**: July 23, 2025  
**Version**: 1.0
