# Requirements for Arabic Company Name Transliterator
# No external dependencies required - uses only Python standard library

# Optional: For enhanced transliteration (if you want to install additional libraries)
# transliterate>=1.10.2
# pandas>=1.3.0
# openpyxl>=3.0.0

# The main script uses only built-in Python libraries:
# - re (regular expressions)
# - csv (CSV file handling)
# - json (JSON handling)
# - sys (system functions)
# - pathlib (path handling)
# - typing (type hints)
