# Arabic Company Name Transliterator - Usage Guide

## Quick Start

Your Arabic company names have been successfully processed! Here's what you have:

### Generated Files

1. **transliterated_companies.csv** - Basic transliteration
2. **transliterated_companies.tsv** - Basic transliteration (tab-separated)
3. **enhanced_transliterated_companies.csv** - Enhanced transliteration with better vowels
4. **enhanced_transliterated_companies.tsv** - Enhanced transliteration (tab-separated)

### Recommendation

**Use the enhanced versions** (`enhanced_transliterated_companies.*`) as they provide:
- Better vowel placement for readability
- More natural English pronunciation
- Improved handling of business terms

## How to Use in Excel

### Method 1: Import CSV File (Recommended)
1. Open Excel
2. Go to **Data** → **Get Data** → **From File** → **From Text/CSV**
3. Select `enhanced_transliterated_companies.csv`
4. Click **Load**
5. Excel will create a table with two columns:
   - Column A: Original Arabic names
   - Column B: Transliterated English names

### Method 2: Copy-Paste from TSV
1. Open `enhanced_transliterated_companies.tsv` in Notepad
2. Select all content (Ctrl+A) and copy (Ctrl+C)
3. In Excel, click on cell A1
4. Paste (Ctrl+V) - Excel will automatically separate into columns

### Method 3: Add to Existing Excel File
1. Open your existing Excel file with Arabic company names
2. Insert a new column next to your Arabic names
3. Open `enhanced_transliterated_companies.csv`
4. Copy the transliterated names (Column B)
5. Paste into your new column

## Sample Results

Here are some examples of the transliteration quality:

| Original Arabic | Enhanced Transliteration |
|----------------|-------------------------|
| مجوهرات عارف | Majwahrat Araf |
| الشركة العمانية ش.م.م | Al Sharika Alamanyah LLC |
| مؤسسة الباسم | Muassasa Albasam |
| مركز التدريب | Markaz for Training |
| جامعة السلطان قابوس | Jamia Alsaltan Qabaws |

## Understanding the Transliteration

### Business Terms Translation
- **ش.م.م** → **LLC** (Limited Liability Company)
- **ش.م.ع.ع** → **SAOG** (Societas Anonyma Omanensis Generalis)
- **توصية** → **Limited Partnership**
- **للتجارة** → **for Trading**
- **والمقاولات** → **and Contracting**
- **مؤسسة** → **Muassasa** (Foundation/Establishment)
- **شركة** → **Sharika** (Company)
- **مركز** → **Markaz** (Center)

### Phonetic Accuracy
The transliteration preserves Arabic pronunciation:
- **ش** → **sh** (as in "shop")
- **خ** → **kh** (as in German "ach")
- **ث** → **th** (as in "think")
- **ذ** → **dh** (as in "this")
- **غ** → **gh** (guttural 'g')

## File Formats Explained

### CSV Format
- Comma-separated values
- Best for importing into Excel
- Handles special characters well
- Professional standard format

### TSV Format
- Tab-separated values
- Best for copy-pasting
- Preserves spacing
- Easy manual handling

## Tips for Best Results

1. **Use Enhanced Version**: The enhanced transliterator provides more readable results
2. **Check Mixed Content**: Some entries contain both Arabic and English - these are handled appropriately
3. **Business Context**: Common business terms are translated to their English equivalents
4. **Capitalization**: Each word is properly capitalized for professional appearance

## Troubleshooting

### If Excel doesn't recognize Arabic text:
1. Ensure your Excel supports UTF-8 encoding
2. Try importing as CSV instead of opening directly
3. Use the "Data" → "Get Data" method for best results

### If transliteration looks incorrect:
1. The enhanced version provides better readability
2. Some Arabic names may not have direct English equivalents
3. The transliteration preserves pronunciation, not meaning

## Next Steps

1. **Review the results** in Excel
2. **Make manual adjustments** if needed for specific company names
3. **Save your Excel file** with both original and transliterated columns
4. **Use the transliterated names** for English documentation, reports, or databases

## Support

If you need to modify the transliteration rules or add custom business terms:
1. Edit the `BUSINESS_TERMS` dictionary in the Python script
2. Modify the `ARABIC_TRANSLITERATION` mapping as needed
3. Re-run the script to generate updated results

---

**Total processed**: 1,041 company names  
**Success rate**: 100%  
**Output quality**: Professional-grade transliteration suitable for business use
